package com.checklist.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;

// Import all the classes that need to be declared as beans
import com.checklist.controller.ChecklistTemplateController;
import com.checklist.controller.ChecklistReviewController;
import com.checklist.controller.HealthController;
import com.checklist.controller.TemplateConfigController;
import com.checklist.controller.TemplateVersionController;
import com.checklist.service.ChecklistTemplateService;
import com.checklist.service.ChecklistReviewService;
import com.checklist.service.ExcelImportService;
import com.checklist.service.FileStorageService;
import com.checklist.service.TemplateVersionService;
import com.checklist.util.JsonFileUtil;
import com.checklist.repository.ChecklistTemplateRepository;
import com.checklist.repository.ChecklistReviewRepository;
import com.checklist.exception.GlobalExceptionHandler;

/**
 * Spring MVC Web Configuration
 * 显式声明所有Bean，替代组件扫描
 */
@Configuration
@EnableWebMvc
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 配置API端点的CORS
        registry.addMapping("/api/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*");

        // 配置健康检查端点的CORS
        registry.addMapping("/health")
                .allowedOrigins("*")
                .allowedMethods("GET", "OPTIONS")
                .allowedHeaders("*");
    }

    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }

    // Repository层Bean声明
    @Bean
    public ChecklistTemplateRepository checklistTemplateRepository() {
        return new ChecklistTemplateRepository();
    }

    @Bean
    public ChecklistReviewRepository checklistReviewRepository() {
        return new ChecklistReviewRepository();
    }

    // Service层Bean声明
    @Bean
    public ExcelImportService excelImportService() {
        return new ExcelImportService();
    }

    @Bean
    public FileStorageService fileStorageService() {
        return new FileStorageService();
    }

    @Bean
    public ChecklistTemplateService checklistTemplateService() {
        return new ChecklistTemplateService(
            checklistTemplateRepository(),
            excelImportService()
        );
    }

    @Bean
    public ChecklistReviewService checklistReviewService() {
        return new ChecklistReviewService(
            checklistReviewRepository(),
            checklistTemplateRepository()
        );
    }

    @Bean
    public JsonFileUtil jsonFileUtil() {
        return new JsonFileUtil();
    }

    @Bean
    public TemplateVersionService templateVersionService() {
        return new TemplateVersionService();
    }

    // Controller层Bean声明
    @Bean
    public ChecklistTemplateController checklistTemplateController() {
        return new ChecklistTemplateController(checklistTemplateService());
    }

    @Bean
    public ChecklistReviewController checklistReviewController() {
        return new ChecklistReviewController(checklistReviewService());
    }

    @Bean
    public HealthController healthController() {
        return new HealthController();
    }

    @Bean
    public TemplateConfigController templateConfigController() {
        return new TemplateConfigController(checklistTemplateService());
    }

    @Bean
    public TemplateVersionController templateVersionController() {
        return new TemplateVersionController();
    }

    // Exception Handler Bean声明
    @Bean
    public GlobalExceptionHandler globalExceptionHandler() {
        return new GlobalExceptionHandler();
    }
}