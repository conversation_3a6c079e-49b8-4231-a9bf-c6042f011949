package com.checklist.controller;

import com.checklist.model.ChecklistTemplate;
import com.checklist.service.ChecklistTemplateService;
import com.checklist.service.FileStorageService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 模板配置管理控制器
 * 提供统一的模板配置管理接口，以模板ID为维度管理所有配置
 */
@RestController
@RequestMapping("/template-config")
public class TemplateConfigController {

    private final ChecklistTemplateService templateService;
    private final FileStorageService fileStorageService;

    public TemplateConfigController(ChecklistTemplateService templateService, FileStorageService fileStorageService) {
        this.templateService = templateService;
        this.fileStorageService = fileStorageService;
    }

    /**
     * 加载模板的完整配置
     * GET /api/template-config/{templateId}
     */
    @GetMapping("/{templateId}")
    public ResponseEntity<Map<String, Object>> loadTemplateConfig(@PathVariable String templateId) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 获取模板基本信息
            Optional<ChecklistTemplate> templateOpt = templateService.getTemplateById(templateId.trim());
            if (!templateOpt.isPresent()) {
                Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_NOT_FOUND", 
                    "模板不存在: " + templateId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }

            ChecklistTemplate template = templateOpt.get();

            // 构建完整配置响应
            Map<String, Object> configResponse = new HashMap<>();
            configResponse.put("template", template);

            // 加载各种配置信息
            configResponse.put("tableConfig", loadTemplateTableConfig(templateId));
            configResponse.put("defectRules", loadTemplateDefectRules(templateId));
            configResponse.put("statusButtons", loadTemplateStatusButtons(templateId));

            Map<String, Object> response = createSuccessResponse(configResponse);
            return ResponseEntity.ok(response);

        } catch (IOException e) {
            Map<String, Object> errorResponse = createErrorResponse("TEMPLATE_READ_ERROR", 
                "读取模板配置失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取模板配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 保存模板的完整配置
     * PUT /api/template-config/{templateId}
     */
    @PutMapping("/{templateId}")
    public ResponseEntity<Map<String, Object>> saveTemplateConfig(
            @PathVariable String templateId,
            @RequestBody Map<String, Object> configData) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            if (configData == null) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_REQUEST_BODY", 
                    "请求体不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 解析并保存各种配置
            if (configData.containsKey("tableConfig")) {
                saveTemplateTableConfig(templateId, configData.get("tableConfig"));
            }
            if (configData.containsKey("defectRules")) {
                saveTemplateDefectRules(templateId, configData.get("defectRules"));
            }
            if (configData.containsKey("statusButtons")) {
                saveTemplateStatusButtons(templateId, configData.get("statusButtons"));
            }

            // 如果包含基本模板信息，也需要更新模板
            if (configData.containsKey("basicInfo")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> basicInfo = (Map<String, Object>) configData.get("basicInfo");
                updateTemplateBasicInfo(templateId, basicInfo);
            }

            Map<String, Object> response = createSuccessResponse("模板配置保存成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "保存模板配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取模板的表格配置
     * GET /api/template-config/{templateId}/table-config
     */
    @GetMapping("/{templateId}/table-config")
    public ResponseEntity<Map<String, Object>> getTemplateTableConfig(@PathVariable String templateId) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 获取表格配置
            Object tableConfig = loadTemplateTableConfig(templateId);
            Map<String, Object> response = createSuccessResponse(tableConfig);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取表格配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 保存模板的表格配置
     * PUT /api/template-config/{templateId}/table-config
     */
    @PutMapping("/{templateId}/table-config")
    public ResponseEntity<Map<String, Object>> saveTemplateTableConfig(
            @PathVariable String templateId,
            @RequestBody Map<String, Object> tableConfig) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 保存表格配置
            saveTemplateTableConfig(templateId, tableConfig);

            Map<String, Object> response = createSuccessResponse("表格配置保存成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "保存表格配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取模板的缺陷规则配置
     * GET /api/template-config/{templateId}/defect-rules
     */
    @GetMapping("/{templateId}/defect-rules")
    public ResponseEntity<Map<String, Object>> getTemplateDefectRules(@PathVariable String templateId) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 获取缺陷规则
            Object defectRules = loadTemplateDefectRules(templateId);
            Map<String, Object> response = createSuccessResponse(defectRules);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取缺陷规则时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 保存模板的缺陷规则配置
     * PUT /api/template-config/{templateId}/defect-rules
     */
    @PutMapping("/{templateId}/defect-rules")
    public ResponseEntity<Map<String, Object>> saveTemplateDefectRules(
            @PathVariable String templateId,
            @RequestBody Map<String, Object> defectRulesData) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 保存缺陷规则
            saveTemplateDefectRules(templateId, defectRulesData);

            Map<String, Object> response = createSuccessResponse("缺陷规则保存成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "保存缺陷规则时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取模板的状态按钮配置
     * GET /api/template-config/{templateId}/status-buttons
     */
    @GetMapping("/{templateId}/status-buttons")
    public ResponseEntity<Map<String, Object>> getTemplateStatusButtons(@PathVariable String templateId) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 获取状态按钮配置
            Object statusButtons = loadTemplateStatusButtons(templateId);
            Map<String, Object> response = createSuccessResponse(statusButtons);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "获取状态按钮配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 保存模板的状态按钮配置
     * PUT /api/template-config/{templateId}/status-buttons
     */
    @PutMapping("/{templateId}/status-buttons")
    public ResponseEntity<Map<String, Object>> saveTemplateStatusButtons(
            @PathVariable String templateId,
            @RequestBody Map<String, Object> statusButtonsData) {
        try {
            // 参数验证
            if (templateId == null || templateId.trim().isEmpty()) {
                Map<String, Object> errorResponse = createErrorResponse("INVALID_PARAMETER", 
                    "模板ID不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 保存状态按钮配置
            saveTemplateStatusButtons(templateId, statusButtonsData);

            Map<String, Object> response = createSuccessResponse("状态按钮配置保存成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = createErrorResponse("UNKNOWN_ERROR", 
                "保存状态按钮配置时发生未知错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", data);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String errorCode, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", errorCode);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 加载模板的表格配置
     */
    private Object loadTemplateTableConfig(String templateId) {
        try {
            return fileStorageService.loadConfig("table_config_" + templateId, Map.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 加载模板的缺陷规则
     */
    private Object loadTemplateDefectRules(String templateId) {
        try {
            Object rules = fileStorageService.loadConfig("defect_rules_" + templateId, Object.class);
            return rules != null ? rules : new Object[0];
        } catch (Exception e) {
            return new Object[0];
        }
    }

    /**
     * 加载模板的状态按钮配置
     */
    private Object loadTemplateStatusButtons(String templateId) {
        try {
            Object buttons = fileStorageService.loadConfig("status_buttons_" + templateId, Object.class);
            return buttons != null ? buttons : new Object[0];
        } catch (Exception e) {
            return new Object[0];
        }
    }

    /**
     * 保存模板的表格配置
     */
    private void saveTemplateTableConfig(String templateId, Object tableConfig) throws IOException {
        if (tableConfig != null) {
            fileStorageService.saveConfig("table_config_" + templateId, tableConfig);
        }
    }

    /**
     * 保存模板的缺陷规则
     */
    private void saveTemplateDefectRules(String templateId, Object defectRules) throws IOException {
        if (defectRules != null) {
            fileStorageService.saveConfig("defect_rules_" + templateId, defectRules);
        }
    }

    /**
     * 保存模板的状态按钮配置
     */
    private void saveTemplateStatusButtons(String templateId, Object statusButtons) throws IOException {
        if (statusButtons != null) {
            fileStorageService.saveConfig("status_buttons_" + templateId, statusButtons);
        }
    }

    /**
     * 更新模板基本信息
     */
    private void updateTemplateBasicInfo(String templateId, Map<String, Object> basicInfo) throws IOException {
        Optional<ChecklistTemplate> templateOpt = templateService.getTemplateById(templateId);
        if (templateOpt.isPresent()) {
            ChecklistTemplate template = templateOpt.get();

            // 更新基本信息
            if (basicInfo.containsKey("name")) {
                template.setName((String) basicInfo.get("name"));
            }
            if (basicInfo.containsKey("type")) {
                template.setType((String) basicInfo.get("type"));
            }
            if (basicInfo.containsKey("version")) {
                template.setVersion((String) basicInfo.get("version"));
            }

            // 保存更新后的模板
            templateService.updateTemplate(templateId, template);
        }
    }
}
